#!/usr/bin/env python3
"""
Demonstration of the Enhanced SQLi Agent Approach

This script shows how the new specialized tools prevent LLM hallucination
by providing structured, deterministic output for SQLi testing.
"""

import json


def demonstrate_tool_outputs():
    """Show example outputs from the specialized SQLi tools"""
    
    print("ENHANCED SQLI AGENT - SPECIALIZED TOOL OUTPUTS")
    print("=" * 60)
    
    # Example 1: Time-based SQLi tool output
    print("\n1. test_time_based_sqli() - VULNERABLE CASE:")
    print("-" * 50)
    time_based_vulnerable = {
        "is_vulnerable": True,
        "baseline_duration_sec": 0.234,
        "payload_duration_sec": 5.187,
        "time_difference_sec": 4.953,
        "expected_delay_sec": 5,
        "payload_used": "' AND SLEEP(5)--",
        "conclusion": "Time-based SQLi CONFIRMED: Payload caused 4.95s delay (expected: 5s)"
    }
    print(json.dumps(time_based_vulnerable, indent=2))
    
    print("\n1. test_time_based_sqli() - NOT VULNERABLE CASE:")
    print("-" * 50)
    time_based_safe = {
        "is_vulnerable": False,
        "baseline_duration_sec": 0.234,
        "payload_duration_sec": 0.267,
        "time_difference_sec": 0.033,
        "expected_delay_sec": 5,
        "payload_used": "' AND SLEEP(5)--",
        "conclusion": "Time-based SQLi NOT confirmed: Only 0.03s difference (expected: 5s)"
    }
    print(json.dumps(time_based_safe, indent=2))
    
    # Example 2: Error-based SQLi tool output
    print("\n2. test_error_based_sqli() - VULNERABLE CASE:")
    print("-" * 50)
    error_based_vulnerable = {
        "is_vulnerable": True,
        "matched_pattern": "You have an error in your SQL syntax",
        "response_snippet": "...You have an error in your SQL syntax; check the manual...",
        "payload_used": "' AND 1=CONVERT(int, 'text')--",
        "patterns_tested": ["ORA-[0-9]{5}", "MySQL.*Error", "You have an error in your SQL syntax"],
        "response_status_code": 200,
        "conclusion": "Error-based SQLi CONFIRMED: Found database error matching pattern 'You have an error in your SQL syntax'"
    }
    print(json.dumps(error_based_vulnerable, indent=2))
    
    # Example 3: UNION-based SQLi tool output
    print("\n3. test_union_based_sqli() - VULNERABLE CASE:")
    print("-" * 50)
    union_based_vulnerable = {
        "is_vulnerable": True,
        "correct_column_count": 3,
        "payload_used": "' UNION SELECT NULL,NULL,NULL--",
        "baseline_length": 1234,
        "payload_response_length": 1456,
        "length_difference": 222,
        "baseline_status": 200,
        "payload_status": 200,
        "conclusion": "UNION-based SQLi CONFIRMED: Found correct column count of 3"
    }
    print(json.dumps(union_based_vulnerable, indent=2))
    
    # Example 4: Boolean-based SQLi tool output
    print("\n4. test_boolean_based_sqli() - VULNERABLE CASE:")
    print("-" * 50)
    boolean_based_vulnerable = {
        "is_vulnerable": True,
        "true_payload": "' AND 1=1--",
        "false_payload": "' AND 1=2--",
        "response_true_len": 1234,
        "response_false_len": 987,
        "true_status_code": 200,
        "false_status_code": 200,
        "content_is_different": True,
        "status_is_different": False,
        "conclusion": "Boolean-based SQLi CONFIRMED: TRUE and FALSE conditions produced different responses"
    }
    print(json.dumps(boolean_based_vulnerable, indent=2))


def demonstrate_grounding_benefits():
    """Show how the new approach prevents hallucination"""
    
    print("\n" + "=" * 60)
    print("GROUNDING IMPROVEMENT ANALYSIS")
    print("=" * 60)
    
    print("\nPROBLEM WITH OLD APPROACH (execute_python_code):")
    print("-" * 50)
    print("❌ LLM could fabricate evidence:")
    print("   • 'The response took 5.2 seconds' (when it was actually 0.3s)")
    print("   • 'Found MySQL error: Table doesn\\'t exist' (when no error occurred)")
    print("   • 'UNION injection successful with 3 columns' (without testing)")
    print("   • 'TRUE condition returned different content' (when identical)")
    
    print("\n❌ LLM had too much freedom to interpret raw HTTP responses")
    print("❌ No validation of claims made by the LLM")
    print("❌ High false positive rate due to hallucination")
    
    print("\nSOLUTION WITH NEW APPROACH (specialized tools):")
    print("-" * 50)
    print("✅ Mathematical validation:")
    print("   • test_time_based_sqli() measures actual response times")
    print("   • Only reports vulnerability if delay >= 80% of expected")
    
    print("\n✅ Pattern matching validation:")
    print("   • test_error_based_sqli() uses regex to find real error messages")
    print("   • Only reports vulnerability if error pattern is found")
    
    print("\n✅ Content comparison validation:")
    print("   • test_union_based_sqli() compares response lengths and status codes")
    print("   • test_boolean_based_sqli() performs byte-by-byte content comparison")
    
    print("\n✅ Structured output prevents misinterpretation:")
    print("   • Clear 'is_vulnerable' boolean field")
    print("   • Evidence fields contain actual measured values")
    print("   • 'conclusion' field provides human-readable summary")
    
    print("\nRESULT:")
    print("-" * 20)
    print("🎯 LLM can only report vulnerabilities with tool-confirmed evidence")
    print("🎯 False positives dramatically reduced")
    print("🎯 Domain logic separated from strategic reasoning")
    print("🎯 Reliable, reproducible vulnerability detection")


def show_enhanced_system_prompt():
    """Show key parts of the enhanced system prompt"""
    
    print("\n" + "=" * 60)
    print("ENHANCED SYSTEM PROMPT KEY DIRECTIVES")
    print("=" * 60)
    
    prompt_excerpt = '''
**YOUR PRIMARY DIRECTIVE: USE THE RIGHT TOOL AND TRUST ITS OUTPUT**
Your ONLY method for testing is to use the provided tools. The 'conclusion' 
field in the tool's JSON output is the source of truth. You MUST NOT report 
a vulnerability unless a tool explicitly confirms it with "is_vulnerable": true.

**General Testing Workflow:**
1. **Analyze Batch & Prioritize**: Review requests to identify promising candidates
2. **Form a Hypothesis**: Decide which type of SQLi is most likely
3. **Select and Use the Correct Tool**: Call the specific tool that matches your hypothesis
4. **Analyze the Tool's JSON Output**: Examine the structured JSON returned
5. **Report Based on Evidence**: Only report vulnerabilities with tool confirmation

**DO NOT USE `execute_python_code` for simple testing. It is a last resort for 
complex, multi-step exploits ONLY AFTER a vulnerability has been confirmed.**

Rules:
- TRUST THE TOOL OUTPUT: If a tool returns "is_vulnerable": false, do NOT claim vulnerability
- Use the most appropriate tool for each hypothesis
- Only use execute_python_code for exploitation AFTER confirming vulnerability
'''
    
    print(prompt_excerpt)


if __name__ == "__main__":
    demonstrate_tool_outputs()
    demonstrate_grounding_benefits()
    show_enhanced_system_prompt()
    
    print("\n" + "=" * 60)
    print("IMPLEMENTATION COMPLETE!")
    print("=" * 60)
    print("\nThe enhanced SQLi agent now includes:")
    print("✅ 4 specialized, self-validating tools")
    print("✅ Grounded system prompt that enforces tool usage")
    print("✅ Structured JSON outputs that prevent hallucination")
    print("✅ Clear separation between domain logic and LLM strategy")
    print("\nThis approach should dramatically reduce false positives")
    print("and provide reliable SQLi vulnerability detection.")
