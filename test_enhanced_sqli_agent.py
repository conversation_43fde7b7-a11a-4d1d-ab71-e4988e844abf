#!/usr/bin/env python3
"""
Test script for the enhanced SQLi agent with specialized tools
"""

import asyncio
import json
import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from vapt.sqli.sqli_tools import (
    test_time_based_sqli,
    test_error_based_sqli,
    test_union_based_sqli,
    test_boolean_based_sqli
)


def test_specialized_tools():
    """Test the specialized SQLi tools with sample requests"""
    
    # Sample raw HTTP request for testing
    sample_request = """GET /search?q=test&id=1 HTTP/1.1
Host: vulnerable-app.com
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
Accept-Language: en-US,en;q=0.5
Accept-Encoding: gzip, deflate
Connection: keep-alive

"""

    print("Testing Enhanced SQLi Agent Tools")
    print("=" * 50)
    
    # Test 1: Time-based SQLi
    print("\n1. Testing Time-Based SQLi Detection:")
    print("-" * 40)
    try:
        time_result = test_time_based_sqli.func(
            raw_request=sample_request,
            payload="' AND SLEEP(5)--",
            delay_seconds=5
        )
        print(f"Result: {json.dumps(time_result, indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        print("Note: This would normally test against a real vulnerable endpoint")

    # Test 2: Error-based SQLi
    print("\n2. Testing Error-Based SQLi Detection:")
    print("-" * 40)
    try:
        error_result = test_error_based_sqli.func(
            raw_request=sample_request,
            payload="' AND 1=CONVERT(int, 'text')--",
            error_patterns=[
                r"ORA-[0-9]{5}",
                r"MySQL.*Error",
                r"PostgreSQL.*ERROR",
                r"syntax error at or near",
                r"You have an error in your SQL syntax"
            ]
        )
        print(f"Result: {json.dumps(error_result, indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        print("Note: This would normally test against a real vulnerable endpoint")

    # Test 3: UNION-based SQLi
    print("\n3. Testing UNION-Based SQLi Detection:")
    print("-" * 40)
    try:
        union_result = test_union_based_sqli.func(
            raw_request=sample_request,
            max_columns=5
        )
        print(f"Result: {json.dumps(union_result, indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        print("Note: This would normally test against a real vulnerable endpoint")

    # Test 4: Boolean-based SQLi
    print("\n4. Testing Boolean-Based SQLi Detection:")
    print("-" * 40)
    try:
        boolean_result = test_boolean_based_sqli.func(
            raw_request=sample_request,
            true_payload="' AND 1=1--",
            false_payload="' AND 1=2--"
        )
        print(f"Result: {json.dumps(boolean_result, indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        print("Note: This would normally test against a real vulnerable endpoint")
    
    print("\n" + "=" * 50)
    print("Enhanced SQLi Agent Tool Testing Complete!")
    print("\nKey Benefits of the Enhanced Approach:")
    print("- Structured, deterministic output prevents LLM hallucination")
    print("- Each tool provides clear 'is_vulnerable' boolean with evidence")
    print("- Tools encapsulate domain-specific testing logic")
    print("- LLM focuses on strategy rather than low-level HTTP analysis")


def demonstrate_grounding_improvement():
    """Demonstrate how the new tools prevent hallucination"""
    
    print("\n" + "=" * 60)
    print("GROUNDING IMPROVEMENT DEMONSTRATION")
    print("=" * 60)
    
    print("\nOLD APPROACH PROBLEMS:")
    print("- LLM could hallucinate time delays that didn't exist")
    print("- LLM could invent database error messages")
    print("- LLM could claim successful UNION injection without evidence")
    print("- LLM could fabricate differences between TRUE/FALSE responses")
    
    print("\nNEW APPROACH SOLUTIONS:")
    print("- test_time_based_sqli() measures actual response times mathematically")
    print("- test_error_based_sqli() uses regex to find real error patterns")
    print("- test_union_based_sqli() systematically tests column counts")
    print("- test_boolean_based_sqli() compares actual response content")
    
    print("\nRESULT:")
    print("- LLM can only report vulnerabilities with tool-confirmed evidence")
    print("- Structured JSON output prevents misinterpretation")
    print("- Domain logic is separated from strategic reasoning")
    print("- False positives are dramatically reduced")


if __name__ == "__main__":
    test_specialized_tools()
    demonstrate_grounding_improvement()
